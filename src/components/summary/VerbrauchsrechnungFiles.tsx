import React from 'react';

export interface VerbrauchsrechnungFilesProps {
  certificateId: string;
  files: any[];
}

/**
 * Component for displaying consumption bill files
 * Extracted from the original ZusammenfassungPage
 */
export const VerbrauchsrechnungFiles: React.FC<VerbrauchsrechnungFilesProps> = ({
  files
}) => {
  if (!files || files.length === 0) {
    return null;
  }

  return (
    <div className="bg-white shadow rounded-lg p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">
        Verbrauchsrechnungen
      </h3>
      <div className="space-y-3">
        {files.map((file, index) => (
          <div key={index} className="border rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <span className="text-sm text-gray-900 truncate">
                {file.name || `Verbrauchsrechnung ${index + 1}`}
              </span>
            </div>
            {file.size && (
              <p className="text-xs text-gray-500 mt-1">
                {(file.size / 1024 / 1024).toFixed(2)} MB
              </p>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};