import React from 'react';

export interface GebaeudebildFilesProps {
  certificateId: string;
  files: any[];
}

/**
 * Component for displaying building image files
 * Extracted from the original ZusammenfassungPage
 */
export const GebaeudebildFiles: React.FC<GebaeudebildFilesProps> = ({
  files
}) => {
  if (!files || files.length === 0) {
    return null;
  }

  return (
    <div className="bg-white shadow rounded-lg p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">
        Gebäudebilder
      </h3>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {files.map((file, index) => (
          <div key={index} className="border rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <span className="text-sm text-gray-900 truncate">
                {file.name || `Gebäudebild ${index + 1}`}
              </span>
            </div>
            {file.size && (
              <p className="text-xs text-gray-500 mt-1">
                {(file.size / 1024 / 1024).toFixed(2)} MB
              </p>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};