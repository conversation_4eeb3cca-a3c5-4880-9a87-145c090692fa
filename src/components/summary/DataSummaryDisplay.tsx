import React from 'react';
import { DataSection } from '../ui/DataSection';
import { type SectionConfig } from '../../hooks/useSummaryData';
import { GebaeudebildFiles } from './GebaeudebildFiles';
import { VerbrauchsrechnungFiles } from './VerbrauchsrechnungFiles';
import { GrundrissFiles } from './GrundrissFiles';

export interface DataSummaryDisplayProps {
  sections: SectionConfig[];
  filesByField?: Record<string, any[]>;
  activeCertificateId?: string | null;
}

/**
 * Main data display component using existing DataSection components
 * Renders all data sections and handles file display components
 */
export const DataSummaryDisplay: React.FC<DataSummaryDisplayProps> = ({
  sections,
  filesByField = {},
  activeCertificateId
}) => {
  
  console.log('Rendering DataSummaryDisplay');

  return (
    <div className="space-y-8 mb-8">
      {/* Render all data sections */}
      {sections.map((section, index) => (
        <DataSection
          key={`${section.title}-${index}`}
          title={section.title}
          data={section.data}
          fields={section.fields}
          excludeFields={section.excludeFields}
        />
      ))}

      {/* File Display Sections */}
      {activeCertificateId && (
        <>
          {/* Gebäudebilder */}
          <GebaeudebildFiles
            certificateId={activeCertificateId}
            files={filesByField.gebaeudebild || []}
          />

          {/* Verbrauchsrechnungen */}
          <VerbrauchsrechnungFiles
            certificateId={activeCertificateId}
            files={[
              ...(filesByField.verbrauchsrechnung1 || []),
              ...(filesByField.verbrauchsrechnung2 || []),
              ...(filesByField.verbrauchsrechnung3 || [])
            ]}
          />

          {/* Grundrisse */}
          <GrundrissFiles
            certificateId={activeCertificateId}
            files={filesByField.grundriss || []}
          />
        </>
      )}
    </div>
  );
};