import React from 'react';
import { fieldLabels } from '../../utils/fieldLabels';
import { enumValueLabels } from '../../utils/enumValueLabels';

export interface DataSectionProps {
  title: string;
  data: Record<string, any> | null | undefined;
  fields?: string[] | Record<string, string>;
  excludeFields?: string[];
}

/**
 * Component for displaying a section of data in a formatted table
 * Uses field labels and enum value labels for user-friendly display
 */
export const DataSection: React.FC<DataSectionProps> = ({
  title,
  data,
  fields,
  excludeFields = []
}) => {
  if (!data || Object.keys(data).length === 0) {
    return null;
  }

  // Determine which fields to display
  const getFieldsToDisplay = () => {
    if (Array.isArray(fields)) {
      // If fields is an array, use those specific fields
      return fields.filter(field => !excludeFields.includes(field));
    } else if (fields && typeof fields === 'object') {
      // If fields is an object, use its keys
      return Object.keys(fields).filter(field => !excludeFields.includes(field));
    } else {
      // Otherwise, use all fields from data except excluded ones
      return Object.keys(data).filter(field => 
        !excludeFields.includes(field) && 
        data[field] !== null && 
        data[field] !== undefined && 
        data[field] !== ''
      );
    }
  };

  const fieldsToDisplay = getFieldsToDisplay();

  if (fieldsToDisplay.length === 0) {
    return null;
  }

  // Get display label for a field
  const getFieldLabel = (field: string): string => {
    if (fields && typeof fields === 'object' && !Array.isArray(fields)) {
      const fieldMap = fields as Record<string, string>;
      if (fieldMap[field]) {
        return fieldMap[field];
      }
    }
    return fieldLabels[field] || field;
  };

  // Get display value for a field
  const getDisplayValue = (field: string, value: any): string => {
    if (value === null || value === undefined || value === '') {
      return '-';
    }

    // Check if there's an enum mapping for this field
    if (enumValueLabels[field] && enumValueLabels[field][value]) {
      return enumValueLabels[field][value];
    }

    // Handle boolean values
    if (typeof value === 'boolean') {
      return value ? 'Ja' : 'Nein';
    }

    // Handle arrays (shouldn't normally happen in display, but just in case)
    if (Array.isArray(value)) {
      return value.join(', ');
    }

    // Handle objects (shouldn't normally happen in display, but just in case)
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }

    return String(value);
  };

  return (
    <div className="bg-white shadow rounded-lg p-6 mb-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">
        {title}
      </h3>
      
      <div className="overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Feld
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Wert
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {fieldsToDisplay.map((field, index) => (
              <tr key={field} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {getFieldLabel(field)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                  {getDisplayValue(field, data[field])}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};
